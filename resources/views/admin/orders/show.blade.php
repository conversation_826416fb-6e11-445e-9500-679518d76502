@extends('layouts.dashboard')

@section('title', 'Order Details - ' . $order->order_number)
@section('page-title', 'Order Details')

@section('content')
<div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <div class="flex items-center space-x-4 mb-2">
                <a href="{{ route('admin.orders.index') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-sm transition-all duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Orders
                </a>
            </div>
            <h1 class="text-3xl font-bold text-gray-900">Order {{ $order->order_number }}</h1>
            <p class="mt-2 text-gray-600">Placed on {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex items-center space-x-3">
            @if($order->invoice)
                <a href="#" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-sm transition-all duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download Invoice
                </a>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Order Details -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Order Items -->
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                <div class="px-8 py-6 border-b border-gray-200">
                    <h3 class="text-xl font-bold text-gray-900">Order Items</h3>
                </div>
                <div class="p-8">
                    <div class="space-y-6">
                        @foreach($order->orderItems as $item)
                            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-2xl">
                                @if($item->product->image)
                                    <img src="{{ Storage::url($item->product->image) }}" 
                                         alt="{{ $item->product->title }}" 
                                         class="w-16 h-16 object-cover rounded-xl">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded-xl flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900">{{ $item->product->title }}</h4>
                                    <p class="text-sm text-gray-500">SKU: {{ $item->product->sku }}</p>
                                    <p class="text-sm text-gray-600">{{ $item->product->brand }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">Quantity: {{ $item->quantity }}</p>
                                    <p class="text-sm text-gray-500">Unit Price: {{ number_format($item->unit_price, 2) }} MAD</p>
                                    <p class="text-lg font-semibold text-gray-900">{{ number_format($item->total_price, 2) }} MAD</p>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Order Summary -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Subtotal:</span>
                                <span class="font-medium">{{ number_format($order->subtotal, 2) }} MAD</span>
                            </div>
                            @if($order->discount_amount > 0)
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Discount:</span>
                                    <span class="font-medium text-green-600">-{{ number_format($order->discount_amount, 2) }} MAD</span>
                                </div>
                            @endif
                            <div class="flex justify-between text-lg font-bold border-t pt-3">
                                <span>Total:</span>
                                <span>{{ number_format($order->total_amount, 2) }} MAD</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delivery Information -->
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                <div class="px-8 py-6 border-b border-gray-200">
                    <h3 class="text-xl font-bold text-gray-900">Delivery Information</h3>
                </div>
                <div class="p-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Delivery Address</h4>
                            <p class="text-gray-900">{{ $order->delivery_address }}</p>
                            <p class="text-gray-600">{{ $order->delivery_city }}</p>
                        </div>
                        @if($order->deliveryAssignment)
                            <div>
                                <h4 class="text-sm font-semibold text-gray-700 mb-2">Assigned Delivery Person</h4>
                                <p class="text-gray-900">{{ $order->deliveryAssignment->deliveryPerson->name }}</p>
                                <p class="text-gray-600">{{ $order->deliveryAssignment->deliveryPerson->email }}</p>
                                @if($order->deliveryAssignment->assigned_at)
                                    <p class="text-sm text-gray-500 mt-1">
                                        Assigned: {{ $order->deliveryAssignment->assigned_at->format('M d, Y g:i A') }}
                                    </p>
                                @endif
                            </div>
                        @endif
                    </div>

                    @if($order->deliveryAssignment && $order->deliveryAssignment->delivery_notes)
                        <div class="mt-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Delivery Notes</h4>
                            <div class="bg-gray-50 rounded-xl p-4">
                                <pre class="text-sm text-gray-700 whitespace-pre-wrap">{{ $order->deliveryAssignment->delivery_notes }}</pre>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Order Status & Actions -->
        <div class="space-y-8">
            <!-- Customer Information -->
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Customer Information</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm font-semibold text-gray-700">Name</p>
                            <p class="text-gray-900">{{ $order->user->name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-gray-700">Email</p>
                            <p class="text-gray-900">{{ $order->user->email }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-gray-700">User Type</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($order->user->user_type === 'reseller') bg-green-100 text-green-800
                                @else bg-blue-100 text-blue-800 @endif">
                                {{ ucfirst($order->user->user_type) }}
                            </span>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-gray-700">Branch</p>
                            <p class="text-gray-900">{{ $order->branch->name }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Status -->
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Order Status</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm font-semibold text-gray-700 mb-2">Current Status</p>
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium
                                @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                @elseif($order->status === 'picked') bg-purple-100 text-purple-800
                                @elseif($order->status === 'out_for_delivery') bg-orange-100 text-orange-800
                                @elseif($order->status === 'delivered') bg-green-100 text-green-800
                                @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                @endif">
                                {{ ucwords(str_replace('_', ' ', $order->status)) }}
                            </span>
                        </div>

                        <div>
                            <p class="text-sm font-semibold text-gray-700 mb-2">Payment Status</p>
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium
                                @if($order->payment_status === 'pending') bg-yellow-100 text-yellow-800
                                @elseif($order->payment_status === 'partial') bg-orange-100 text-orange-800
                                @elseif($order->payment_status === 'paid') bg-green-100 text-green-800
                                @elseif($order->payment_status === 'refunded') bg-red-100 text-red-800
                                @endif">
                                {{ ucfirst($order->payment_status) }}
                            </span>
                        </div>

                        <div>
                            <p class="text-sm font-semibold text-gray-700">Payment Method</p>
                            <p class="text-gray-900">{{ ucwords(str_replace('_', ' ', $order->payment_method)) }}</p>
                        </div>
                    </div>

                    <!-- Status Update Form -->
                    @if($order->status !== 'delivered' && $order->status !== 'cancelled')
                        <form action="{{ route('admin.orders.update-status', $order) }}" method="POST" class="mt-6">
                            @csrf
                            @method('PATCH')
                            <div class="space-y-4">
                                <div>
                                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Update Status</label>
                                    <select name="status" id="status" class="block w-full py-2 px-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                                        <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="confirmed" {{ $order->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                        <option value="picked" {{ $order->status === 'picked' ? 'selected' : '' }}>Picked</option>
                                        <option value="out_for_delivery" {{ $order->status === 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                                        <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                        <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="notes" class="block text-sm font-semibold text-gray-700 mb-2">Notes (Optional)</label>
                                    <textarea name="notes" id="notes" rows="3" class="block w-full py-2 px-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm" placeholder="Add status update notes..."></textarea>
                                </div>
                                <button type="submit" class="w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-2 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                                    Update Status
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Delivery Assignment -->
            @if(!$order->deliveryAssignment && $order->status !== 'cancelled')
                <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Assign Delivery</h3>
                    </div>
                    <div class="p-6">
                        <form action="{{ route('admin.orders.assign-delivery', $order) }}" method="POST">
                            @csrf
                            <div class="space-y-4">
                                <div>
                                    <label for="delivery_person_id" class="block text-sm font-semibold text-gray-700 mb-2">Delivery Person</label>
                                    <select name="delivery_person_id" id="delivery_person_id" required class="block w-full py-2 px-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm">
                                        <option value="">Select delivery person...</option>
                                        @foreach($deliveryPersons as $person)
                                            <option value="{{ $person->id }}">{{ $person->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div>
                                    <label for="delivery_notes" class="block text-sm font-semibold text-gray-700 mb-2">Notes (Optional)</label>
                                    <textarea name="notes" id="delivery_notes" rows="3" class="block w-full py-2 px-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm" placeholder="Special delivery instructions..."></textarea>
                                </div>
                                <button type="submit" class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-2 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                                    Assign Delivery Person
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
