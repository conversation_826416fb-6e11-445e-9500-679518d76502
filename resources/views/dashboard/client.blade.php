@extends('layouts.dashboard')

@section('title', 'Dashboard')
@section('page-title', 'Welcome to YalaOffice')

@section('content')
<div class="space-y-8">
    <!-- Welcome Banner -->
    <div class="bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 rounded-3xl shadow-2xl overflow-hidden">
        <div class="relative px-8 py-12 sm:px-12 sm:py-16">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-white/5">
                <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
            </div>

            <div class="relative flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-4">
                        <h1 class="text-4xl font-bold text-white">
                            Welcome back, {{ auth()->user()->name }}!
                        </h1>
                        <span class="text-3xl">👋</span>
                    </div>
                    <p class="text-xl text-primary-100 mb-6">
                        @if(auth()->user()->isReseller())
                            🎯 Enjoy special reseller pricing on all products
                        @else
                            🛍️ Discover our latest office and school supplies
                        @endif
                    </p>
                    @if(auth()->user()->isReseller())
                        <div class="inline-flex items-center px-4 py-2 bg-yellow-400 text-yellow-900 rounded-full text-sm font-semibold">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Reseller Account
                        </div>
                    @endif
                </div>
                <div class="hidden lg:block">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <svg class="h-20 w-20 text-white/80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Total Orders -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-100">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-blue-100 rounded-xl p-3 group-hover:bg-blue-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{{ $stats['total_orders'] }}</dd>
                            <dd class="text-sm text-gray-500 font-medium">All time orders</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-100">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-yellow-100 rounded-xl p-3 group-hover:bg-yellow-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Orders</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">{{ $stats['pending_orders'] }}</dd>
                            <dd class="text-sm text-yellow-600 font-medium">
                                @if($stats['pending_orders'] > 0)
                                    Awaiting processing
                                @else
                                    All caught up!
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wishlist Items -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-100">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-red-100 rounded-xl p-3 group-hover:bg-red-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Wishlist Items</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-red-600 transition-colors duration-300">0</dd>
                            <dd class="text-sm text-gray-500 font-medium">Saved for later</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Categories -->
    <div class="bg-white shadow-xl rounded-3xl overflow-hidden border border-gray-100">
        <div class="px-8 py-8 sm:p-10">
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Browse Categories</h3>
                    <p class="mt-2 text-gray-600">Explore our wide range of office and school supplies</p>
                </div>
                <div class="hidden sm:block">
                    <div class="bg-primary-50 rounded-2xl p-3">
                        <svg class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                @forelse($stats['categories'] as $category)
                    <div class="group relative bg-gradient-to-br from-gray-50 to-white p-8 rounded-2xl border border-gray-200 hover:border-primary-300 hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
                        <!-- Category Icon -->
                        <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                            @if($category->image)
                                <img src="{{ Storage::url($category->image) }}" alt="{{ $category->name }}" class="w-8 h-8 text-white">
                            @else
                                <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            @endif
                        </div>

                        <!-- Category Content -->
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                                <a href="#" class="focus:outline-none">
                                    <span class="absolute inset-0" aria-hidden="true"></span>
                                    {{ $category->name }}
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm leading-relaxed mb-4">
                                {{ $category->description ?? 'Explore products in this category' }}
                            </p>
                            <div class="flex items-center text-primary-600 text-sm font-semibold group-hover:text-primary-700 transition-colors duration-300">
                                <span>Explore products</span>
                                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </div>
                        </div>

                        <!-- Hover Effect -->
                        <div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-primary-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-16">
                        <div class="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No categories available</h3>
                        <p class="text-gray-500">Categories will appear here once they are added by the admin.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Featured Products</h3>
                    <p class="mt-1 text-sm text-gray-500">Check out our most popular items</p>
                </div>
                <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                    View all products →
                </a>
            </div>
            <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                @forelse($stats['featured_products'] as $product)
                    <div class="group relative bg-white border border-gray-200 rounded-lg flex flex-col overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 sm:aspect-none sm:h-48">
                            @if($product->featured_image)
                                <img src="{{ $product->featured_image }}" alt="{{ $product->title }}" class="w-full h-full object-center object-cover sm:w-full sm:h-full">
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <svg class="h-12 w-12 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1 p-4 space-y-2 flex flex-col">
                            <h3 class="text-sm font-medium text-gray-900">
                                <a href="#">
                                    <span aria-hidden="true" class="absolute inset-0"></span>
                                    {{ $product->title }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-500 flex-1">{{ Str::limit($product->description, 60) }}</p>
                            <div class="flex items-center justify-between">
                                <p class="text-lg font-medium text-gray-900">
                                    {{ number_format($product->getPriceForUser(auth()->user()->user_type), 2) }} Dh
                                </p>
                                @if(auth()->user()->isReseller() && $product->reseller_price < $product->normal_price)
                                    <span class="text-xs text-green-600 font-medium">Reseller Price</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No featured products</h3>
                        <p class="mt-1 text-sm text-gray-500">Featured products will appear here once they are added.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    @if($stats['recent_orders']->count() > 0)
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Orders</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Your latest orders</p>
            </div>
            <ul class="divide-y divide-gray-200">
                @foreach($stats['recent_orders'] as $order)
                    <li>
                        <div class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'delivered') bg-green-100 text-green-800
                                            @else bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            Order #{{ $order->order_number }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $order->created_at->format('M d, Y') }}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-900">
                                    {{ number_format($order->total_amount, 2) }} Dh
                                </div>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>
@endsection
