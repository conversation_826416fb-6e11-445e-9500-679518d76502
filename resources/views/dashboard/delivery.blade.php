@extends('layouts.dashboard')

@section('title', 'Delivery Dashboard')
@section('page-title', 'Delivery Dashboard')

@section('content')
<div class="space-y-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-orange-600 via-orange-700 to-orange-800 rounded-2xl shadow-xl overflow-hidden">
        <div class="px-8 py-12 sm:px-12 sm:py-16">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">
                        Welcome back, {{ auth()->user()->name }}! 🚚
                    </h1>
                    <p class="text-orange-100 text-lg">
                        Ready to deliver excellence today? Here's your delivery overview.
                    </p>
                </div>
                <div class="hidden lg:block">
                    <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                        <svg class="h-16 w-16 text-white/80" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Assigned Orders -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-blue-100 rounded-xl p-3 group-hover:bg-blue-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Assigned Orders</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{{ $stats['assigned_orders'] }}</dd>
                            <dd class="text-sm text-blue-600 font-medium">Ready for pickup</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivered Today -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-green-100 rounded-xl p-3 group-hover:bg-green-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Delivered Today</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">{{ $stats['delivered_today'] }}</dd>
                            <dd class="text-sm text-green-600 font-medium">Great progress!</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Pickups -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-yellow-100 rounded-xl p-3 group-hover:bg-yellow-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Pickups</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">{{ $stats['pending_pickups'] }}</dd>
                            <dd class="text-sm text-yellow-600 font-medium">Awaiting pickup</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance -->
        <div class="group bg-white overflow-hidden shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-purple-100 rounded-xl p-3 group-hover:bg-purple-200 transition-colors duration-300">
                            <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Performance</dt>
                            <dd class="text-3xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">Excellent</dd>
                            <dd class="text-sm text-purple-600 font-medium">Keep it up! 🌟</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- My Orders -->
    <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
        <div class="px-8 py-8 sm:p-10">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">My Assigned Orders</h3>
                    <p class="mt-2 text-gray-600">Orders assigned to you for delivery</p>
                </div>
                <div class="hidden sm:block">
                    <div class="bg-orange-50 rounded-2xl p-3">
                        <svg class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        <ul class="divide-y divide-gray-200">
            @forelse($stats['my_orders'] as $order)
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($order->status === 'confirmed') bg-blue-100 text-blue-800
                                        @elseif($order->status === 'picked') bg-yellow-100 text-yellow-800
                                        @elseif($order->status === 'out_for_delivery') bg-orange-100 text-orange-800
                                        @elseif($order->status === 'delivered') bg-green-100 text-green-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                    </span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        Order #{{ $order->order_number }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ $order->user->name }} • {{ $order->delivery_city }}
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        Assigned: {{ $order->deliveryAssignment->assigned_at->format('M d, Y H:i') }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="text-sm text-gray-900">
                                    {{ number_format($order->total_amount, 2) }} Dh
                                </div>
                                @if($order->status === 'confirmed')
                                    <button class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        Mark as Picked
                                    </button>
                                @elseif($order->status === 'picked')
                                    <button class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        Out for Delivery
                                    </button>
                                @elseif($order->status === 'out_for_delivery')
                                    <button class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Mark as Delivered
                                    </button>
                                @endif
                            </div>
                        </div>
                        @if($order->delivery_address)
                            <div class="mt-2 text-sm text-gray-600">
                                <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {{ $order->delivery_address }}
                            </div>
                        @endif
                        @if($order->notes)
                            <div class="mt-2 text-sm text-gray-600">
                                <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                </svg>
                                {{ $order->notes }}
                            </div>
                        @endif
                    </div>
                </li>
            @empty
                <li class="px-4 py-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No orders assigned</h3>
                    <p class="mt-1 text-sm text-gray-500">You don't have any orders assigned for delivery at the moment.</p>
                </li>
            @endforelse
        </ul>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
            <div class="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <button class="relative block w-full bg-white p-6 border-2 border-gray-300 border-dashed rounded-lg text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                    </svg>
                    <span class="mt-2 block text-sm font-medium text-gray-900">View Route Map</span>
                </button>

                <button class="relative block w-full bg-white p-6 border-2 border-gray-300 border-dashed rounded-lg text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="mt-2 block text-sm font-medium text-gray-900">Delivery Report</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
