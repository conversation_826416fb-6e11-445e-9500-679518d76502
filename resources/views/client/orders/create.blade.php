@extends('layouts.dashboard')

@section('title', 'Checkout')
@section('page-title', 'Checkout')

@section('content')
<div class="space-y-8">
    <!-- Header -->
    <div class="flex items-center space-x-4 mb-8">
        <a href="{{ route('dashboard') }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-sm transition-all duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
        </a>
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Checkout</h1>
            <p class="mt-2 text-gray-600">Review your order and complete your purchase</p>
        </div>
    </div>

    <form action="{{ route('client.orders.store') }}" method="POST" id="checkout-form">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Order Details Form -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Delivery Information -->
                <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                    <div class="px-8 py-6 border-b border-gray-200">
                        <h3 class="text-xl font-bold text-gray-900">Delivery Information</h3>
                    </div>
                    <div class="p-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Branch Selection -->
                            <div class="md:col-span-2">
                                <label for="branch_id" class="block text-sm font-semibold text-gray-700 mb-2">Select Branch *</label>
                                <select name="branch_id" id="branch_id" required class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">
                                    <option value="">Choose a branch...</option>
                                    @foreach($branches as $branch)
                                        <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }} - {{ $branch->address }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('branch_id')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Delivery Address -->
                            <div class="md:col-span-2">
                                <label for="delivery_address" class="block text-sm font-semibold text-gray-700 mb-2">Delivery Address *</label>
                                <textarea name="delivery_address" id="delivery_address" rows="3" required 
                                          placeholder="Enter your complete delivery address..."
                                          class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">{{ old('delivery_address') }}</textarea>
                                @error('delivery_address')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Delivery City -->
                            <div>
                                <label for="delivery_city" class="block text-sm font-semibold text-gray-700 mb-2">City *</label>
                                <input type="text" name="delivery_city" id="delivery_city" required 
                                       value="{{ old('delivery_city') }}" placeholder="Enter city"
                                       class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">
                                @error('delivery_city')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label for="payment_method" class="block text-sm font-semibold text-gray-700 mb-2">Payment Method *</label>
                                <select name="payment_method" id="payment_method" required class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">
                                    <option value="">Select payment method...</option>
                                    <option value="cash" {{ old('payment_method') === 'cash' ? 'selected' : '' }}>Cash on Delivery</option>
                                    <option value="check" {{ old('payment_method') === 'check' ? 'selected' : '' }}>Check</option>
                                    <option value="bank_transfer" {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                </select>
                                @error('payment_method')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Promo Code -->
                <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                    <div class="px-8 py-6 border-b border-gray-200">
                        <h3 class="text-xl font-bold text-gray-900">Promo Code</h3>
                    </div>
                    <div class="p-8">
                        <div class="flex space-x-4">
                            <div class="flex-1">
                                <input type="text" name="promo_code" id="promo_code" 
                                       value="{{ old('promo_code') }}" placeholder="Enter promo code"
                                       class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">
                            </div>
                            <button type="button" id="apply-promo" 
                                    class="px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                                Apply
                            </button>
                        </div>
                        <div id="promo-message" class="mt-3 text-sm"></div>
                    </div>
                </div>

                <!-- Order Notes -->
                <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                    <div class="px-8 py-6 border-b border-gray-200">
                        <h3 class="text-xl font-bold text-gray-900">Order Notes</h3>
                    </div>
                    <div class="p-8">
                        <textarea name="notes" id="notes" rows="4" 
                                  placeholder="Any special instructions or notes for your order..."
                                  class="block w-full py-3 px-4 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200">{{ old('notes') }}</textarea>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="space-y-8">
                <!-- Cart Items -->
                <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Order Summary</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($orderItems as $item)
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                                    @if($item['product']->image)
                                        <img src="{{ Storage::url($item['product']->image) }}" 
                                             alt="{{ $item['product']->title }}" 
                                             class="w-12 h-12 object-cover rounded-lg">
                                    @else
                                        <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">{{ $item['product']->title }}</p>
                                        <p class="text-xs text-gray-500">{{ $item['quantity'] }} × {{ number_format($item['unit_price'], 2) }} MAD</p>
                                    </div>
                                    <div class="text-sm font-semibold text-gray-900">
                                        {{ number_format($item['total_price'], 2) }} MAD
                                    </div>
                                </div>
                                
                                <!-- Hidden inputs for cart items -->
                                <input type="hidden" name="cart_items[{{ $loop->index }}][product_id]" value="{{ $item['product']->id }}">
                                <input type="hidden" name="cart_items[{{ $loop->index }}][quantity]" value="{{ $item['quantity'] }}">
                            @endforeach
                        </div>

                        <!-- Order Totals -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span class="font-medium" id="subtotal-amount">{{ number_format($subtotal, 2) }} MAD</span>
                                </div>
                                <div class="flex justify-between text-sm" id="discount-row" style="display: none;">
                                    <span class="text-gray-600">Discount:</span>
                                    <span class="font-medium text-green-600" id="discount-amount">-0.00 MAD</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t pt-3">
                                    <span>Total:</span>
                                    <span id="total-amount">{{ number_format($subtotal, 2) }} MAD</span>
                                </div>
                            </div>
                        </div>

                        <!-- Place Order Button -->
                        <button type="submit" 
                                class="w-full mt-6 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Place Order
                        </button>

                        <!-- Security Notice -->
                        <div class="mt-4 p-3 bg-green-50 rounded-xl">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                                <span class="text-sm text-green-800 font-medium">Secure checkout protected by SSL encryption</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyPromoBtn = document.getElementById('apply-promo');
    const promoCodeInput = document.getElementById('promo_code');
    const promoMessage = document.getElementById('promo-message');
    const discountRow = document.getElementById('discount-row');
    const discountAmount = document.getElementById('discount-amount');
    const totalAmount = document.getElementById('total-amount');
    const subtotalAmount = document.getElementById('subtotal-amount');
    
    const subtotal = {{ $subtotal }};
    let currentDiscount = 0;

    applyPromoBtn.addEventListener('click', function() {
        const promoCode = promoCodeInput.value.trim();
        
        if (!promoCode) {
            showPromoMessage('Please enter a promo code.', 'error');
            return;
        }

        // Show loading state
        applyPromoBtn.disabled = true;
        applyPromoBtn.textContent = 'Applying...';

        fetch('{{ route("client.orders.apply-promo-code") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                promo_code: promoCode,
                subtotal: subtotal
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentDiscount = data.discount_amount;
                discountAmount.textContent = '-' + data.discount_amount.toFixed(2) + ' MAD';
                totalAmount.textContent = data.total_amount.toFixed(2) + ' MAD';
                discountRow.style.display = 'flex';
                showPromoMessage(data.message, 'success');
            } else {
                showPromoMessage(data.message, 'error');
                resetDiscount();
            }
        })
        .catch(error => {
            showPromoMessage('An error occurred. Please try again.', 'error');
            resetDiscount();
        })
        .finally(() => {
            applyPromoBtn.disabled = false;
            applyPromoBtn.textContent = 'Apply';
        });
    });

    function showPromoMessage(message, type) {
        promoMessage.textContent = message;
        promoMessage.className = `mt-3 text-sm ${type === 'success' ? 'text-green-600' : 'text-red-600'}`;
    }

    function resetDiscount() {
        currentDiscount = 0;
        discountRow.style.display = 'none';
        totalAmount.textContent = subtotal.toFixed(2) + ' MAD';
    }
});
</script>
@endpush
@endsection
