@php
    $user = auth()->user();
@endphp

<!-- Dashboard Link -->
<a href="{{ route('dashboard') }}"
   class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 {{ request()->routeIs('dashboard') ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
    <div class="flex items-center justify-center w-8 h-8 rounded-lg {{ request()->routeIs('dashboard') ? 'bg-white/20' : 'bg-gray-100 group-hover:bg-gray-200' }} mr-3 transition-colors duration-200">
        <svg class="h-5 w-5 {{ request()->routeIs('dashboard') ? 'text-white' : 'text-gray-500 group-hover:text-gray-700' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
    </div>
    Dashboard
</a>

@if($user->isAdmin() || $user->isManager())
    <!-- Section Header -->
    <div class="px-4 py-2">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Management</h3>
    </div>

    <!-- Users Management -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
        </div>
        Users
    </a>

    <!-- Products Management -->
    <a href="{{ route('admin.products.index') }}"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 {{ request()->routeIs('admin.products.*') ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {{ request()->routeIs('admin.products.*') ? 'bg-white/20' : 'bg-gray-100 group-hover:bg-gray-200' }} mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 {{ request()->routeIs('admin.products.*') ? 'text-white' : 'text-gray-500 group-hover:text-gray-700' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
        </div>
        Products
    </a>

    <!-- Categories Management -->
    <a href="{{ route('admin.categories.index') }}"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 {{ request()->routeIs('admin.categories.*') ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {{ request()->routeIs('admin.categories.*') ? 'bg-white/20' : 'bg-gray-100 group-hover:bg-gray-200' }} mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 {{ request()->routeIs('admin.categories.*') ? 'text-white' : 'text-gray-500 group-hover:text-gray-700' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
        </div>
        Categories
    </a>

    <!-- Branches Management -->
    <a href="{{ route('admin.branches.index') }}"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 {{ request()->routeIs('admin.branches.*') ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900' }}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {{ request()->routeIs('admin.branches.*') ? 'bg-white/20' : 'bg-gray-100 group-hover:bg-gray-200' }} mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 {{ request()->routeIs('admin.branches.*') ? 'text-white' : 'text-gray-500 group-hover:text-gray-700' }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
        </div>
        Branches
    </a>

    <!-- Orders Management -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
        </div>
        Orders
    </a>

    <!-- Analytics -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
        </div>
        Analytics
    </a>

    <!-- Invoices -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        Invoices
    </a>

    <!-- Promo Codes -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
        </div>
        Promo Codes
    </a>

    <!-- Reviews -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
        </div>
        Reviews
    </a>
@endif

@if($user->isDelivery())
    <!-- Section Header -->
    <div class="px-4 py-2">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Delivery</h3>
    </div>

    <!-- My Deliveries -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
            </svg>
        </div>
        My Deliveries
    </a>
@endif

@if($user->isClient() || $user->isReseller())
    <!-- Section Header -->
    <div class="px-4 py-2">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Shopping</h3>
    </div>

    <!-- Browse Products -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
        </div>
        Browse Products
    </a>

    <!-- My Orders -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 8l2 2 4-4" />
            </svg>
        </div>
        My Orders
    </a>

    <!-- Wishlist -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
        </div>
        Wishlist
    </a>

    <!-- My Invoices -->
    <a href="#"
       class="group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 text-gray-600 hover:bg-gray-100 hover:text-gray-900">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 group-hover:bg-gray-200 mr-3 transition-colors duration-200">
            <svg class="h-5 w-5 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        My Invoices
    </a>
@endif
