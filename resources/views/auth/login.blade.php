<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'YalaOffice') }} - Login</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans text-gray-900 antialiased bg-gray-50">
    <div class="min-h-screen flex">
        <!-- Left Column - Forms -->
        <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white">
            <div class="mx-auto w-full max-w-sm lg:w-96">
                <!-- Logo -->
                <div class="flex items-center justify-center mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl p-3 shadow-lg">
                            <svg class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent">YalaOffice</h1>
                            <p class="text-sm text-gray-500 font-medium">Supply Chain Management</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">
                        Welcome Back! 👋
                    </h2>
                    <p class="text-gray-600">
                        Sign in to your account or
                        <a href="{{ route('register') }}" class="font-semibold text-primary-600 hover:text-primary-500 transition-colors duration-200">
                            create a new account
                        </a>
                    </p>
                </div>

                <div class="space-y-8">
                    <!-- Session Status -->
                    @if (session('status'))
                        <div class="bg-green-50 border border-green-200 text-green-800 px-6 py-4 rounded-2xl shadow-sm">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-green-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span class="font-medium">{{ session('status') }}</span>
                            </div>
                        </div>
                    @endif

                    <!-- Login Form -->
                    <div x-data="{ activeTab: 'login' }" class="space-y-8">
                        <!-- Tab Navigation -->
                        <div class="flex space-x-1 bg-gray-100 p-1.5 rounded-2xl shadow-inner">
                            <button @click="activeTab = 'login'"
                                    :class="activeTab === 'login' ? 'bg-white shadow-lg text-gray-900 scale-105' : 'text-gray-500 hover:text-gray-700'"
                                    class="flex-1 py-3 px-4 text-sm font-semibold rounded-xl transition-all duration-200">
                                Log In
                            </button>
                            <button @click="activeTab = 'register'"
                                    :class="activeTab === 'register' ? 'bg-white shadow-lg text-gray-900 scale-105' : 'text-gray-500 hover:text-gray-700'"
                                    class="flex-1 py-3 px-4 text-sm font-semibold rounded-xl transition-all duration-200">
                                Sign Up
                            </button>
                        </div>

                        <!-- Login Form -->
                        <div x-show="activeTab === 'login'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                            <form method="POST" action="{{ route('login') }}" class="space-y-6">
                                @csrf

                                <div class="space-y-2">
                                    <label for="email" class="block text-sm font-semibold text-gray-700">
                                        Email address
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                            </svg>
                                        </div>
                                        <input id="email" name="email" type="email" autocomplete="email" required
                                               value="{{ old('email') }}" placeholder="Enter your email"
                                               class="appearance-none block w-full pl-12 pr-4 py-4 border border-gray-200 rounded-2xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200 text-sm">
                                    </div>
                                    @error('email')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            {{ $message }}
                                        </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="password" class="block text-sm font-semibold text-gray-700">
                                        Password
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                        </div>
                                        <input id="password" name="password" type="password" autocomplete="current-password" required
                                               placeholder="Enter your password"
                                               class="appearance-none block w-full pl-12 pr-4 py-4 border border-gray-200 rounded-2xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200 text-sm">
                                    </div>
                                    @error('password')
                                        <p class="mt-2 text-sm text-red-600 flex items-center">
                                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            {{ $message }}
                                        </p>
                                    @enderror
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <input id="remember_me" name="remember" type="checkbox"
                                               class="h-5 w-5 text-primary-600 focus:ring-primary-500 border-gray-300 rounded-lg">
                                        <label for="remember_me" class="ml-3 block text-sm font-medium text-gray-700">
                                            Remember me
                                        </label>
                                    </div>

                                    <div class="text-sm">
                                        <a href="{{ route('password.request') }}" class="font-semibold text-primary-600 hover:text-primary-500 transition-colors duration-200">
                                            Forgot password?
                                        </a>
                                    </div>
                                </div>

                                <div>
                                    <button type="submit"
                                            class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-sm font-semibold rounded-2xl text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                                        <svg class="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                        </svg>
                                        Sign in to your account
                                    </button>
                                </div>

                                <!-- Demo Accounts -->
                                <div class="mt-6 p-4 bg-blue-50 rounded-2xl border border-blue-200">
                                    <h4 class="text-sm font-semibold text-blue-900 mb-3">🚀 Demo Accounts</h4>
                                    <div class="grid grid-cols-1 gap-2 text-xs">
                                        <div class="flex justify-between items-center">
                                            <span class="text-blue-700 font-medium">Admin:</span>
                                            <code class="bg-blue-100 px-2 py-1 rounded text-blue-800"><EMAIL></code>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-blue-700 font-medium">Client:</span>
                                            <code class="bg-blue-100 px-2 py-1 rounded text-blue-800"><EMAIL></code>
                                        </div>
                                        <div class="text-center mt-2 text-blue-600">
                                            <span class="font-medium">Password:</span> <code class="bg-blue-100 px-2 py-1 rounded">password</code>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Register Form -->
                        <div x-show="activeTab === 'register'" x-transition>
                            <form method="POST" action="{{ route('register') }}" class="space-y-6">
                                @csrf

                                <div>
                                    <label for="register_name" class="block text-sm font-medium text-gray-700">
                                        Full Name
                                    </label>
                                    <div class="mt-1">
                                        <input id="register_name" name="name" type="text" autocomplete="name" required
                                               value="{{ old('name') }}"
                                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>
                                </div>

                                <div>
                                    <label for="register_email" class="block text-sm font-medium text-gray-700">
                                        Email address
                                    </label>
                                    <div class="mt-1">
                                        <input id="register_email" name="email" type="email" autocomplete="email" required
                                               value="{{ old('email') }}"
                                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>
                                </div>

                                <div>
                                    <label for="register_password" class="block text-sm font-medium text-gray-700">
                                        Password
                                    </label>
                                    <div class="mt-1">
                                        <input id="register_password" name="password" type="password" autocomplete="new-password" required
                                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>
                                </div>

                                <div>
                                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                                        Confirm Password
                                    </label>
                                    <div class="mt-1">
                                        <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required
                                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    </div>
                                </div>

                                <div>
                                    <button type="submit"
                                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        Create Account
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Welcome Content -->
        <div class="hidden lg:block relative w-0 flex-1">
            <div class="absolute inset-0 h-full w-full bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 30px 30px;"></div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                <div class="absolute bottom-32 left-16 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000"></div>

                <div class="relative h-full flex flex-col justify-center px-12 text-white">
                    <div class="max-w-lg">
                        <div class="mb-8">
                            <div class="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-semibold mb-6">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                                Trusted by 500+ Businesses
                            </div>
                            <h1 class="text-5xl font-bold mb-6 leading-tight">
                                Transform Your
                                <span class="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                                    Supply Chain
                                </span>
                            </h1>
                            <p class="text-xl mb-8 text-primary-100 leading-relaxed">
                                Revolutionize your business operations with intelligent management tools designed for modern enterprises.
                            </p>
                        </div>

                        <div class="space-y-6">
                            <div class="flex items-center group">
                                <div class="flex-shrink-0 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-white font-semibold">500+ Businesses Across Morocco</p>
                                    <p class="text-primary-200 text-sm">From Casablanca to Marrakech</p>
                                </div>
                            </div>

                            <div class="flex items-center group">
                                <div class="flex-shrink-0 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-white font-semibold">Lightning Fast Operations</p>
                                    <p class="text-primary-200 text-sm">Streamline your workflow</p>
                                </div>
                            </div>

                            <div class="flex items-center group">
                                <div class="flex-shrink-0 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-white font-semibold">Enterprise Security</p>
                                    <p class="text-primary-200 text-sm">Your data is protected</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-12 p-6 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white font-semibold">Need Help?</p>
                                    <p class="text-primary-200 text-sm">Get in touch with our team</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-white font-semibold">📧 <EMAIL></p>
                                    <p class="text-primary-200 text-sm">📞 +212 600 00 00 00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                © 2025 YalaOffice. Modern Supply Chain Management.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                © 2025 YalaOffice. Modern Supply Chain Management.
            </p>
        </div>
    </footer>
</body>
</html>
