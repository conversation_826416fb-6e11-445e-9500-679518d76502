@extends('layouts.dashboard')

@section('title', 'My Deliveries')
@section('page-title', 'My Deliveries')

@section('content')
<div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">My Deliveries</h1>
            <p class="mt-2 text-gray-600">Manage your assigned delivery orders</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('delivery.orders.route') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-sm transition-all duration-200">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
                View Route Map
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
        <div class="p-8">
            <form method="GET" action="{{ route('delivery.orders.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-2">Search Orders</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Search by order number or customer..."
                               class="block w-full pl-10 pr-3 py-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200 text-sm">
                    </div>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Delivery Status</label>
                    <select name="status" id="status" class="block w-full py-3 px-3 border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-gray-50 focus:bg-white transition-all duration-200 text-sm">
                        <option value="">All Orders</option>
                        <option value="confirmed" {{ request('status') === 'confirmed' ? 'selected' : '' }}>Ready for Pickup</option>
                        <option value="picked" {{ request('status') === 'picked' ? 'selected' : '' }}>Picked Up</option>
                        <option value="out_for_delivery" {{ request('status') === 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                        <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="flex items-end space-x-3">
                    <button type="submit" class="flex-1 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
                        Filter
                    </button>
                    <a href="{{ route('delivery.orders.index') }}" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl text-center transition-all duration-200">
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="space-y-6">
        @forelse($orders as $order)
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300">
                <div class="p-8">
                    <!-- Order Header -->
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Order {{ $order->order_number }}</h3>
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>{{ $order->created_at->format('M d, Y \a\t g:i A') }}</span>
                                <span>•</span>
                                <span>{{ $order->user->name }}</span>
                                <span>•</span>
                                <span class="font-semibold">{{ number_format($order->total_amount, 2) }} MAD</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 mt-4 sm:mt-0">
                            <!-- Order Status -->
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium
                                @if($order->status === 'confirmed') bg-blue-100 text-blue-800
                                @elseif($order->status === 'picked') bg-purple-100 text-purple-800
                                @elseif($order->status === 'out_for_delivery') bg-orange-100 text-orange-800
                                @elseif($order->status === 'delivered') bg-green-100 text-green-800
                                @endif">
                                {{ ucwords(str_replace('_', ' ', $order->status)) }}
                            </span>
                            
                            <!-- Payment Method -->
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ ucwords(str_replace('_', ' ', $order->payment_method)) }}
                            </span>
                        </div>
                    </div>

                    <!-- Customer & Delivery Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- Customer Info -->
                        <div class="bg-blue-50 rounded-2xl p-4">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="bg-blue-100 rounded-full p-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-blue-900">Customer Information</p>
                                </div>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-blue-900">{{ $order->user->name }}</p>
                                <p class="text-xs text-blue-700">{{ $order->user->email }}</p>
                                @if($order->user->phone)
                                    <p class="text-xs text-blue-700">📞 {{ $order->user->phone }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Delivery Address -->
                        <div class="bg-green-50 rounded-2xl p-4">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="bg-green-100 rounded-full p-2">
                                    <svg class="w-5 h-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-green-900">Delivery Address</p>
                                </div>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm text-green-900">{{ $order->delivery_address }}</p>
                                <p class="text-xs text-green-700">{{ $order->delivery_city }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items Preview -->
                    <div class="mb-6">
                        <h4 class="text-sm font-semibold text-gray-700 mb-3">Order Items ({{ $order->orderItems->count() }})</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach($order->orderItems->take(3) as $item)
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                                    @if($item->product->image)
                                        <img src="{{ Storage::url($item->product->image) }}" 
                                             alt="{{ $item->product->title }}" 
                                             class="w-10 h-10 object-cover rounded-lg">
                                    @else
                                        <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="flex-1 min-w-0">
                                        <p class="text-xs font-medium text-gray-900 truncate">{{ $item->product->title }}</p>
                                        <p class="text-xs text-gray-500">Qty: {{ $item->quantity }}</p>
                                    </div>
                                </div>
                            @endforeach
                            
                            @if($order->orderItems->count() > 3)
                                <div class="flex items-center justify-center p-3 bg-gray-50 rounded-xl">
                                    <span class="text-xs text-gray-500">+{{ $order->orderItems->count() - 3 }} more</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Delivery Timeline -->
                    @if($order->deliveryAssignment)
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-3">Delivery Timeline</h4>
                            <div class="flex items-center space-x-4 text-xs">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 rounded-full {{ $order->deliveryAssignment->assigned_at ? 'bg-green-400' : 'bg-gray-300' }}"></div>
                                    <span class="text-gray-600">Assigned</span>
                                    @if($order->deliveryAssignment->assigned_at)
                                        <span class="text-gray-500">({{ $order->deliveryAssignment->assigned_at->format('M d, g:i A') }})</span>
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 rounded-full {{ $order->deliveryAssignment->picked_at ? 'bg-green-400' : 'bg-gray-300' }}"></div>
                                    <span class="text-gray-600">Picked</span>
                                    @if($order->deliveryAssignment->picked_at)
                                        <span class="text-gray-500">({{ $order->deliveryAssignment->picked_at->format('M d, g:i A') }})</span>
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 rounded-full {{ $order->deliveryAssignment->out_for_delivery_at ? 'bg-green-400' : 'bg-gray-300' }}"></div>
                                    <span class="text-gray-600">Out for Delivery</span>
                                    @if($order->deliveryAssignment->out_for_delivery_at)
                                        <span class="text-gray-500">({{ $order->deliveryAssignment->out_for_delivery_at->format('M d, g:i A') }})</span>
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 rounded-full {{ $order->deliveryAssignment->delivered_at ? 'bg-green-400' : 'bg-gray-300' }}"></div>
                                    <span class="text-gray-600">Delivered</span>
                                    @if($order->deliveryAssignment->delivered_at)
                                        <span class="text-gray-500">({{ $order->deliveryAssignment->delivered_at->format('M d, g:i A') }})</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Order Actions -->
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
                        <div class="flex items-center space-x-4">
                            <a href="{{ route('delivery.orders.show', $order) }}" 
                               class="inline-flex items-center px-4 py-2 border border-primary-300 text-sm font-medium rounded-xl text-primary-700 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                View Details
                            </a>
                        </div>

                        <!-- Quick Status Update -->
                        @if($order->status !== 'delivered')
                            <div class="flex items-center space-x-2">
                                @if($order->status === 'confirmed')
                                    <form action="{{ route('delivery.orders.update-status', $order) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="picked">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-xl text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Mark as Picked
                                        </button>
                                    </form>
                                @elseif($order->status === 'picked')
                                    <form action="{{ route('delivery.orders.update-status', $order) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="out_for_delivery">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-xl text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200">
                                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                                            </svg>
                                            Out for Delivery
                                        </button>
                                    </form>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="bg-white shadow-xl rounded-3xl border border-gray-100 overflow-hidden">
                <div class="p-12 text-center">
                    <div class="flex flex-col items-center">
                        <div class="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mb-6">
                            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No deliveries assigned</h3>
                        <p class="text-gray-500">You don't have any delivery orders assigned yet. Check back later for new assignments.</p>
                    </div>
                </div>
            </div>
        @endforelse
    </div>

    @if($orders->hasPages())
        <div class="flex justify-center">
            {{ $orders->links() }}
        </div>
    @endif
</div>
@endsection
