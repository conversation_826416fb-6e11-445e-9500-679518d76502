# YalaOffice - Stock and Order Management System
## Complete Development Specification

---

## 🎯 Project Overview

**YalaOffice** is a modern web-based application designed for managing stock and orders for office and school supplies in both wholesale and retail operations. The system supports multi-level user access, multi-branch operations, and comprehensive order processing with integrated payment gateways.

**Target Market**: Office and school supply businesses in Morocco  
**Currency**: Moroccan Dirham (Dh)  
**Language**: English  

---

## 🏗️ Technology Stack

### Backend
- **Laravel 11** - PHP framework with MVC architecture
- **PHP 8.2+** - Server-side programming language
- **MySQL/SQLite** - Database management system
- **Eloquent ORM** - Database abstraction layer

### Frontend
- **Blade Templates** - <PERSON><PERSON>'s templating engine
- **Tailwind CSS v4** - Utility-first CSS framework
- **Vite** - Frontend build tool and development server
- **Alpine.js** - Lightweight JavaScript framework for interactivity

### Development Tools
- **Composer** - PHP dependency manager
- **NPM** - Node.js package manager
- **Laravel Artisan** - Command-line interface
- **<PERSON><PERSON> Tinker** - Interactive shell

### Deployment & Infrastructure
- **Self-hosted** - Target deployment platform
- **MAMP/XAMPP** - Local development environment

### Additional Integrations
- **Authentication**: Laravel's built-in auth with third-party support (Google, Facebook)
- **Map Integration**: Free map API (OpenStreetMap), default to Morocco
- **Payment Gateways**: Check, Cash, Bank Transfer
- **Email Notifications**: Laravel Mail
- **PDF Generation**: For invoices and reports

---

## 👥 User Roles and Permissions

### 1. System Administrator (Admin)
**Full system control with the following capabilities:**

#### Dashboard Access:
- Users Management
- Products Management
- Categories Management
- Branch Management
- Order Management
- Analytics & Reporting
- Reviews & Comments
- Promo Codes Management
- System Settings
- Invoices Management

#### Key Features:
- ✅ Create all types of user accounts
- ✅ Make orders on behalf of clients/resellers
- ✅ Manage user information and account details
- ✅ Change order and payment statuses
- ✅ View system-wide live statistics
- ✅ Manage promotional offers
- ✅ Account security and profile management

#### Quick Actions:
- Add new products
- Add new categories
- Add new users
- Manage orders

### 2. Store Administrator (Manager)
**Similar to System Admin but without system-wide settings:**

#### Dashboard Access:
- Users Management
- Products Management
- Categories Management
- Branch Management
- Order Management
- Reviews & Comments
- Promo Codes Management
- Invoices Management

#### Key Features:
- ✅ View and analyze statistics
- ✅ Same quick actions as Admin
- ✅ Account security and profile management

### 3. Delivery Person
**Order fulfillment focused role:**

#### Key Features:
- ✅ View assigned orders
- ✅ Update delivery status: "Picked" → "Out for Delivery" → "Delivered"
- ✅ Collect digital/physical signatures from customers
- ✅ Manage personal account and security settings

### 4. Normal Client
**Standard customer access:**

#### Key Features:
- ✅ View product listings with standard pricing
- ✅ Create and manage personal accounts
- ✅ Monitor invoices and stock availability
- ✅ Place and manage orders
- ✅ Track order status
- ✅ Manage delivery addresses and payment preferences
- ✅ Manage wishlist products
- ✅ Account security settings

### 5. Reseller
**Enhanced customer with wholesale pricing:**

#### Key Features:
- ✅ All Normal Client features
- ✅ Access to reseller pricing (wholesale rates)
- ✅ Same functionality as Normal Client but with different pricing tier

---

## 🔐 Authentication System

### Login Page Design
**Two-column layout:**

#### Left Column:
- Log In form
- Sign Up form
- Reset Password form

#### Right Column:
- Background image with gradient overlay
- Welcome content:
  ```
  Join 500+ businesses already using YalaOffice
  Welcome to YalaOffice
  Revolutionize your supply chain operations with intelligent management tools.
  Trusted by 500+ Businesses Across Morocco
  Get in Touch @ <EMAIL> 📞 +212 600 00 00 00 💬 WhatsApp: +212 600 00 00 00
  ```

#### Footer:
```
© 2025 YalaOffice. Modern Supply Chain Management.
```

### Security Features:
- Role-based access control
- SSL encryption for sensitive data
- Two-factor authentication for admins and managers
- Password reset functionality

---

## 📦 Core System Features

### 1. Inventory Management

#### Product Information Structure:
- **Featured Image**: Recommended 800px × 800px
- **Gallery Images**: Max 4 images, 800px × 800px each
- **Title**: Product name
- **Description**: Detailed product description
- **Category**: Predefined categories and subcategories
- **Pricing**: 
  - Normal client price
  - Reseller price
- **Brand**: Product brand
- **Current Stock**: Available quantity
- **Minimum Stock**: Low stock threshold
- **Tags**: Optional product tags
- **Branch**: Assigned branch location

#### Features:
- ✅ Multi-branch inventory support
- ✅ Stock level monitoring
- ✅ Low-stock alerts and restocking reminders
- ✅ Product categorization system
- ✅ Dual pricing structure (normal/reseller)

### 2. Order Management System

#### Order Workflow:
1. **Customer Browsing**: Browse products, add to cart/wishlist
2. **Checkout Process**: 
   - Apply appropriate pricing based on user type
   - Enter promo codes for discounts
   - Mark delivery address on map
   - Select payment method
3. **Order Processing**:
   - Automatic notification to administrators
   - Admin/Manager approval and assignment to delivery personnel
4. **Delivery Management**:
   - Status updates: Picked → Out for Delivery → Delivered
   - Digital signature collection
5. **Invoice Generation**: PDF invoices when payment confirmed

#### Payment Options:
- Check
- Cash
- Bank Transfer
- Partial payment support

#### Order Tracking:
- Real-time status updates for customers
- Admin dashboard for order management

### 3. User Management

#### Account Management Features:
- User registration and authentication
- Profile management (Full Name, Email, Phone, Address, City)
- Password management and security settings
- Role-based access control
- Admin tools for user creation/editing/removal

### 4. Notification System

#### Email Notifications:
- Order confirmations
- Low stock alerts
- Order status updates
- Delivery notifications
- System changes (new users/products/categories/branches)

#### In-App Notifications:
- Real-time updates for all user types
- Relevant event notifications

---

## 📊 Analytics & Reporting

### System-wide Statistics (Admin View):
- **Sales Metrics**: Total orders, sales, revenue
- **Inventory Metrics**: Stock levels, low-stock items
- **Performance Metrics**: Branch-wise performance
- **Product Analytics**: 
  - Most popular products
  - Best-selling items
  - Most requested categories and brands
- **User Analytics**: 
  - Latest users
  - Top customers
- **Time-based Reports**: Yearly, Monthly, Weekly, Daily
- **Invoice Management**: Payment statuses and tracking

### Report Types:
- Detailed sales reports for managers
- Delivery efficiency reports
- Inventory reports
- Customer interaction records

---

## 🛒 Customer Experience Flow

### Registration Process:
1. Scan QR code (optional entry point)
2. Create account
3. Email verification

### Shopping Experience:
1. **Login** to dashboard
2. **Browse** product categories and promotional offers
3. **Product Discovery**: 
   - View latest products with pagination
   - Filter, sort, and search functionality
4. **Shopping Cart**: Add products, manage quantities
5. **Wishlist**: Save products for later
6. **Checkout Process**:
   - Enter delivery information
   - Mark exact address on map
   - Select payment method
   - Apply promo codes
7. **Order Confirmation**: Thank you page with order details
8. **Order Tracking**: Monitor delivery status

### Account Management:
- Profile settings and management
- Order history
- Invoice management
- Security settings
- Notification preferences

---

## 🚚 Delivery Workflow

### Process Steps:
1. **Assignment**: Admin/Manager assigns order to delivery person
2. **Pickup**: Delivery person collects order from store, updates status to "Picked"
3. **Transit**: Updates status to "Out for Delivery"
4. **Delivery**: 
   - Collects customer signature
   - Updates status to "Delivered"
   - System automatically sends invoice to client

### Status Updates:
- Real-time status changes across all system interfaces
- Automated notifications to relevant parties

---

## 🎨 User Interface Requirements

### Dashboard Design:
Each user type has a personalized dashboard featuring:
- **Product Categories**: Grid layout for easy browsing
- **Promotional Offers**: Sliding banners/cards
- **Latest Products**: Paginated product listings with:
  - Filtering options
  - Sorting capabilities
  - Search functionality
- **Notifications Panel**: User-relevant updates
- **Account Management**: Profile and security settings
- **Quick Actions**: Role-appropriate shortcuts

### Data Tables (All Record Pages):
- **Pagination**: 20 records per page
- **Filtering**: Advanced filter options
- **Sorting**: Column-based sorting
- **Search**: Real-time search functionality
- **Export**: CSV export capability
- **Import**: CSV import functionality
- **Coverage**: Users, products, categories, branches

---

## 📋 System Architecture Requirements

### Multi-branch Support:
- Assign specific inventory to different branches across Morocco
- Branch-wise performance tracking
- Centralized management with distributed inventory

### Scalability:
- Support for additional branches
- Expandable product catalog
- User base growth accommodation

### Map Integration:
- Free map API implementation (OpenStreetMap recommended)
- Default location: Morocco
- Customer address marking for delivery
- Integration in checkout process

### Data Management:
- Local storage for cart management (initial implementation)
- Session management for user preferences
- Real-time inventory updates

---

## 🔧 Development Guidelines

### Code Organization:
- Follow Laravel 11 best practices
- Implement MVC architecture
- Use Eloquent ORM for database operations
- Utilize Laravel's built-in features (Auth, Mail, etc.)

### Frontend Development:
- Responsive design with Tailwind CSS
- Interactive elements with Alpine.js
- Blade template inheritance
- Component-based approach

### Security Implementation:
- Input validation and sanitization
- CSRF protection
- SQL injection prevention
- XSS protection
- Secure file upload handling

### Performance Considerations:
- Database query optimization
- Image optimization for product galleries
- Caching strategies
- Efficient pagination

---

## 📝 Deliverables Checklist

### Phase 1: Core System
- [ ] Authentication system with all user roles
- [ ] Basic dashboard for each user type
- [ ] Product management system
- [ ] Category management
- [ ] User management
- [ ] Basic order placement

### Phase 2: Advanced Features
- [ ] Payment gateway integration
- [ ] Email notification system
- [ ] PDF invoice generation
- [ ] Map integration for delivery
- [ ] Advanced filtering and search

### Phase 3: Analytics & Optimization
- [ ] Analytics dashboard
- [ ] Reporting system
- [ ] Performance optimization
- [ ] Security enhancements
- [ ] Testing and deployment

### Phase 4: Enhancement Features
- [ ] Promo code system
- [ ] Review and comment system
- [ ] Advanced notification system
- [ ] Mobile responsiveness optimization
- [ ] Third-party authentication integration

---

## 🚀 Getting Started

### Development Setup:
1. Install Laravel 11 and dependencies
2. Configure database (MySQL/SQLite)
3. Set up Tailwind CSS v4 with Vite
4. Install Alpine.js for frontend interactivity
5. Configure email settings for notifications
6. Set up map API integration
7. Create initial database migrations and seeders

### Initial Database Schema:
Plan for tables including:
- Users (with roles)
- Products
- Categories
- Branches
- Orders
- Order Items
- Invoices
- Delivery Assignments
- Notifications
- Promo Codes

---

*This specification serves as the complete development guide for the YalaOffice system. All features should be implemented according to these requirements with attention to user experience, security, and scalability.*