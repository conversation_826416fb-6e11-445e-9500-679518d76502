<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PromoCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_order_amount',
        'usage_limit',
        'used_count',
        'starts_at',
        'expires_at',
        'is_active',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'starts_at' => 'date',
        'expires_at' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get active promo codes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('starts_at', '<=', now())
            ->where('expires_at', '>=', now());
    }

    /**
     * Check if promo code is valid.
     */
    public function isValid($orderAmount = 0): bool
    {
        if (!$this->is_active) return false;
        if ($this->starts_at > now()) return false;
        if ($this->expires_at < now()) return false;
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) return false;
        if ($this->minimum_order_amount && $orderAmount < $this->minimum_order_amount) return false;

        return true;
    }

    /**
     * Calculate discount amount.
     */
    public function calculateDiscount($orderAmount): float
    {
        if (!$this->isValid($orderAmount)) return 0;

        if ($this->type === 'percentage') {
            return ($orderAmount * $this->value) / 100;
        }

        return min($this->value, $orderAmount); // Fixed amount, but not more than order total
    }

    /**
     * Check if promo code can be used (alias for isValid).
     */
    public function canBeUsed($orderAmount): bool
    {
        return $this->isValid($orderAmount);
    }

    /**
     * Get usage error message.
     */
    public function getUsageErrorMessage($orderAmount): string
    {
        if (!$this->is_active) {
            return 'This promo code is not active.';
        }

        if ($this->starts_at > now()) {
            return 'This promo code is not yet valid.';
        }

        if ($this->expires_at < now()) {
            return 'This promo code has expired.';
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return 'This promo code has reached its usage limit.';
        }

        if ($this->minimum_order_amount && $orderAmount < $this->minimum_order_amount) {
            return "Minimum order amount of " . number_format($this->minimum_order_amount, 2) . " required.";
        }

        return 'This promo code cannot be used.';
    }
}
