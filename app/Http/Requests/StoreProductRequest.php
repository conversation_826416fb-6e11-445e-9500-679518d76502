<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->hasRole(['admin', 'manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'sku' => 'required|string|max:100|unique:products,sku',
            'category_id' => 'required|exists:categories,id',
            'branch_id' => 'required|exists:branches,id',
            'brand' => 'nullable|string|max:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'normal_price' => 'required|numeric|min:0',
            'reseller_price' => 'required|numeric|min:0',
            'current_stock' => 'required|integer|min:0',
            'minimum_stock' => 'required|integer|min:0',
            'tags' => 'nullable|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Product title is required.',
            'sku.unique' => 'This SKU is already taken.',
            'category_id.required' => 'Please select a category.',
            'branch_id.required' => 'Please select a branch.',
            'featured_image.image' => 'Featured image must be a valid image file.',
            'gallery_images.*.image' => 'Gallery images must be valid image files.',
            'normal_price.required' => 'Normal price is required.',
            'reseller_price.required' => 'Reseller price is required.',
            'current_stock.required' => 'Current stock is required.',
            'minimum_stock.required' => 'Minimum stock is required.',
        ];
    }
}
