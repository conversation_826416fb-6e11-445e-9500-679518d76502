<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\Branch;
use App\Models\DeliveryAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'branch', 'orderItems.product', 'deliveryAssignment.deliveryPerson']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->latest()->paginate(20);
        $branches = Branch::active()->get();
        $deliveryPersons = User::role('delivery')->get();

        return view('admin.orders.index', compact('orders', 'branches', 'deliveryPersons'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $order->load([
            'user', 
            'branch', 
            'orderItems.product', 
            'deliveryAssignment.deliveryPerson',
            'invoice'
        ]);

        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,picked,out_for_delivery,delivered,cancelled',
            'notes' => 'nullable|string|max:1000'
        ]);

        DB::transaction(function() use ($request, $order) {
            $oldStatus = $order->status;
            $newStatus = $request->status;

            // Update order status
            $order->update([
                'status' => $newStatus,
                'confirmed_at' => $newStatus === 'confirmed' && !$order->confirmed_at ? now() : $order->confirmed_at,
                'delivered_at' => $newStatus === 'delivered' && !$order->delivered_at ? now() : $order->delivered_at,
            ]);

            // Update delivery assignment timestamps
            if ($order->deliveryAssignment) {
                $assignment = $order->deliveryAssignment;
                
                switch ($newStatus) {
                    case 'picked':
                        if (!$assignment->picked_at) {
                            $assignment->update(['picked_at' => now()]);
                        }
                        break;
                    case 'out_for_delivery':
                        if (!$assignment->out_for_delivery_at) {
                            $assignment->update(['out_for_delivery_at' => now()]);
                        }
                        break;
                    case 'delivered':
                        if (!$assignment->delivered_at) {
                            $assignment->update(['delivered_at' => now()]);
                        }
                        break;
                }

                // Add notes if provided
                if ($request->filled('notes')) {
                    $assignment->update([
                        'delivery_notes' => $assignment->delivery_notes . "\n" . now()->format('Y-m-d H:i') . ": " . $request->notes
                    ]);
                }
            }

            // TODO: Send notifications to customer and delivery person
        });

        return back()->with('success', 'Order status updated successfully.');
    }

    /**
     * Assign delivery person to order.
     */
    public function assignDelivery(Request $request, Order $order)
    {
        $request->validate([
            'delivery_person_id' => 'required|exists:users,id',
            'notes' => 'nullable|string|max:500'
        ]);

        // Verify the user is a delivery person
        $deliveryPerson = User::findOrFail($request->delivery_person_id);
        if (!$deliveryPerson->hasRole('delivery')) {
            return back()->with('error', 'Selected user is not a delivery person.');
        }

        DB::transaction(function() use ($request, $order) {
            // Create or update delivery assignment
            DeliveryAssignment::updateOrCreate(
                ['order_id' => $order->id],
                [
                    'delivery_person_id' => $request->delivery_person_id,
                    'assigned_by' => auth()->id(),
                    'assigned_at' => now(),
                    'delivery_notes' => $request->notes
                ]
            );

            // Update order status to confirmed if it's still pending
            if ($order->status === 'pending') {
                $order->update([
                    'status' => 'confirmed',
                    'confirmed_at' => now()
                ]);
            }
        });

        return back()->with('success', 'Delivery person assigned successfully.');
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Request $request, Order $order)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,partial,paid,refunded'
        ]);

        $order->update(['payment_status' => $request->payment_status]);

        return back()->with('success', 'Payment status updated successfully.');
    }

    /**
     * Cancel order.
     */
    public function cancel(Request $request, Order $order)
    {
        if (!$order->canBeCancelled()) {
            return back()->with('error', 'This order cannot be cancelled.');
        }

        $request->validate([
            'cancellation_reason' => 'required|string|max:500'
        ]);

        DB::transaction(function() use ($request, $order) {
            $order->update([
                'status' => 'cancelled',
                'notes' => ($order->notes ? $order->notes . "\n" : '') . 
                          "Cancelled on " . now()->format('Y-m-d H:i') . ": " . $request->cancellation_reason
            ]);

            // TODO: Handle inventory restoration if needed
            // TODO: Send cancellation notifications
        });

        return back()->with('success', 'Order cancelled successfully.');
    }

    /**
     * Get order statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'confirmed_orders' => Order::where('status', 'confirmed')->count(),
            'out_for_delivery' => Order::where('status', 'out_for_delivery')->count(),
            'delivered_today' => Order::where('status', 'delivered')
                                    ->whereDate('delivered_at', today())
                                    ->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => Order::where('payment_status', 'pending')->sum('total_amount'),
        ];

        return response()->json($stats);
    }
}
