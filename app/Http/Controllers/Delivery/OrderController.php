<?php

namespace App\Http\Controllers\Delivery;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\DeliveryAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class OrderController extends Controller
{
    /**
     * Display delivery person's assigned orders.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = Order::whereHas('deliveryAssignment', function($q) use ($user) {
            $q->where('delivery_person_id', $user->id);
        })->with(['user', 'branch', 'orderItems.product', 'deliveryAssignment']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Default to show non-delivered orders first
        if (!$request->filled('status')) {
            $query->where('status', '!=', 'delivered');
        }

        $orders = $query->latest()->paginate(15);

        return view('delivery.orders.index', compact('orders'));
    }

    /**
     * Show order details for delivery.
     */
    public function show(Order $order)
    {
        $user = auth()->user();
        
        // Ensure delivery person can only view their assigned orders
        if (!$order->deliveryAssignment || $order->deliveryAssignment->delivery_person_id !== $user->id) {
            abort(403, 'You are not assigned to this order.');
        }

        $order->load([
            'user', 
            'branch', 
            'orderItems.product', 
            'deliveryAssignment'
        ]);

        return view('delivery.orders.show', compact('order'));
    }

    /**
     * Update order status (pickup, out for delivery, delivered).
     */
    public function updateStatus(Request $request, Order $order)
    {
        $user = auth()->user();
        
        // Ensure delivery person can only update their assigned orders
        if (!$order->deliveryAssignment || $order->deliveryAssignment->delivery_person_id !== $user->id) {
            abort(403, 'You are not assigned to this order.');
        }

        $request->validate([
            'status' => 'required|in:picked,out_for_delivery,delivered',
            'notes' => 'nullable|string|max:1000',
            'customer_signature' => 'nullable|image|max:2048' // For delivered status
        ]);

        $newStatus = $request->status;
        $assignment = $order->deliveryAssignment;

        // Validate status progression
        if (!$this->canUpdateToStatus($order->status, $newStatus)) {
            return back()->with('error', 'Invalid status transition.');
        }

        // Handle customer signature for delivered orders
        $signaturePath = null;
        if ($newStatus === 'delivered' && $request->hasFile('customer_signature')) {
            $signaturePath = $request->file('customer_signature')->store('signatures', 'public');
        }

        // Update order and assignment
        $order->update([
            'status' => $newStatus,
            'delivered_at' => $newStatus === 'delivered' ? now() : $order->delivered_at
        ]);

        // Update assignment timestamps
        $updateData = ['delivery_notes' => $this->appendNotes($assignment->delivery_notes, $request->notes)];
        
        switch ($newStatus) {
            case 'picked':
                $updateData['picked_at'] = now();
                break;
            case 'out_for_delivery':
                $updateData['out_for_delivery_at'] = now();
                break;
            case 'delivered':
                $updateData['delivered_at'] = now();
                if ($signaturePath) {
                    $updateData['customer_signature'] = $signaturePath;
                }
                break;
        }

        $assignment->update($updateData);

        $statusMessages = [
            'picked' => 'Order marked as picked up successfully.',
            'out_for_delivery' => 'Order marked as out for delivery.',
            'delivered' => 'Order marked as delivered successfully.'
        ];

        return back()->with('success', $statusMessages[$newStatus]);
    }

    /**
     * Get delivery route/map data.
     */
    public function getRoute(Request $request)
    {
        $user = auth()->user();
        
        $orders = Order::whereHas('deliveryAssignment', function($q) use ($user) {
            $q->where('delivery_person_id', $user->id);
        })->whereIn('status', ['confirmed', 'picked', 'out_for_delivery'])
          ->whereNotNull('delivery_latitude')
          ->whereNotNull('delivery_longitude')
          ->with(['user', 'deliveryAssignment'])
          ->get();

        $routeData = $orders->map(function($order) {
            return [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'customer_name' => $order->user->name,
                'customer_phone' => $order->user->phone ?? '',
                'address' => $order->delivery_address,
                'city' => $order->delivery_city,
                'latitude' => (float) $order->delivery_latitude,
                'longitude' => (float) $order->delivery_longitude,
                'status' => $order->status,
                'total_amount' => $order->total_amount,
                'payment_method' => $order->payment_method,
                'notes' => $order->notes
            ];
        });

        return response()->json($routeData);
    }

    /**
     * Mark order as problematic.
     */
    public function reportProblem(Request $request, Order $order)
    {
        $user = auth()->user();
        
        // Ensure delivery person can only report problems for their assigned orders
        if (!$order->deliveryAssignment || $order->deliveryAssignment->delivery_person_id !== $user->id) {
            abort(403, 'You are not assigned to this order.');
        }

        $request->validate([
            'problem_type' => 'required|in:customer_not_available,wrong_address,payment_issue,product_damaged,other',
            'problem_description' => 'required|string|max:1000',
            'photo' => 'nullable|image|max:2048'
        ]);

        $photoPath = null;
        if ($request->hasFile('photo')) {
            $photoPath = $request->file('photo')->store('delivery_problems', 'public');
        }

        $problemNote = "PROBLEM REPORTED by " . $user->name . " on " . now()->format('Y-m-d H:i') . "\n";
        $problemNote .= "Type: " . ucwords(str_replace('_', ' ', $request->problem_type)) . "\n";
        $problemNote .= "Description: " . $request->problem_description;
        if ($photoPath) {
            $problemNote .= "\nPhoto: " . $photoPath;
        }

        $assignment = $order->deliveryAssignment;
        $assignment->update([
            'delivery_notes' => $this->appendNotes($assignment->delivery_notes, $problemNote)
        ]);

        // TODO: Send notification to administrators

        return back()->with('success', 'Problem reported successfully. Administrators have been notified.');
    }

    /**
     * Get delivery statistics for dashboard.
     */
    public function getStats()
    {
        $user = auth()->user();
        
        $stats = [
            'assigned_orders' => Order::whereHas('deliveryAssignment', function($q) use ($user) {
                $q->where('delivery_person_id', $user->id);
            })->where('status', '!=', 'delivered')->count(),

            'delivered_today' => Order::whereHas('deliveryAssignment', function($q) use ($user) {
                $q->where('delivery_person_id', $user->id);
            })->where('status', 'delivered')
              ->whereDate('delivered_at', today())
              ->count(),

            'pending_pickups' => Order::whereHas('deliveryAssignment', function($q) use ($user) {
                $q->where('delivery_person_id', $user->id)
                  ->whereNull('picked_at');
            })->count(),

            'out_for_delivery' => Order::whereHas('deliveryAssignment', function($q) use ($user) {
                $q->where('delivery_person_id', $user->id);
            })->where('status', 'out_for_delivery')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Check if status can be updated to new status.
     */
    private function canUpdateToStatus($currentStatus, $newStatus)
    {
        $validTransitions = [
            'confirmed' => ['picked'],
            'picked' => ['out_for_delivery'],
            'out_for_delivery' => ['delivered']
        ];

        return isset($validTransitions[$currentStatus]) && 
               in_array($newStatus, $validTransitions[$currentStatus]);
    }

    /**
     * Append notes to existing delivery notes.
     */
    private function appendNotes($existingNotes, $newNotes)
    {
        if (empty($newNotes)) {
            return $existingNotes;
        }

        $timestamp = now()->format('Y-m-d H:i');
        $formattedNote = "{$timestamp}: {$newNotes}";

        return $existingNotes ? "{$existingNotes}\n{$formattedNote}" : $formattedNote;
    }
}
