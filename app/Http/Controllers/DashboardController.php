<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Category;
use App\Models\Branch;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        switch ($user->user_type) {
            case 'admin':
            case 'manager':
                return $this->adminDashboard();
            case 'delivery':
                return $this->deliveryDashboard();
            case 'client':
            case 'reseller':
                return $this->clientDashboard();
            default:
                return redirect()->route('login');
        }
    }

    private function adminDashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'total_categories' => Category::count(),
            'total_branches' => Branch::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'low_stock_products' => Product::lowStock()->count(),
            'recent_orders' => Order::with(['user', 'branch'])
                ->latest()
                ->take(5)
                ->get(),
        ];

        return view('dashboard.admin', compact('stats'));
    }

    private function deliveryDashboard()
    {
        $user = auth()->user();

        $stats = [
            'assigned_orders' => Order::whereHas('deliveryAssignment', function($query) use ($user) {
                $query->where('delivery_person_id', $user->id);
            })->where('status', '!=', 'delivered')->count(),

            'delivered_today' => Order::whereHas('deliveryAssignment', function($query) use ($user) {
                $query->where('delivery_person_id', $user->id);
            })->where('status', 'delivered')
              ->whereDate('delivered_at', today())
              ->count(),

            'pending_pickups' => Order::whereHas('deliveryAssignment', function($query) use ($user) {
                $query->where('delivery_person_id', $user->id)
                      ->whereNull('picked_at');
            })->count(),

            'my_orders' => Order::whereHas('deliveryAssignment', function($query) use ($user) {
                $query->where('delivery_person_id', $user->id);
            })->with(['user', 'deliveryAssignment'])
              ->where('status', '!=', 'delivered')
              ->latest()
              ->get(),
        ];

        return view('dashboard.delivery', compact('stats'));
    }

    private function clientDashboard()
    {
        $user = auth()->user();

        $stats = [
            'total_orders' => Order::where('user_id', $user->id)->count(),
            'pending_orders' => Order::where('user_id', $user->id)
                ->where('status', 'pending')
                ->count(),
            'recent_orders' => Order::where('user_id', $user->id)
                ->latest()
                ->take(5)
                ->get(),
            'featured_products' => Product::featured()
                ->active()
                ->take(8)
                ->get(),
            'categories' => Category::active()
                ->parents()
                ->take(6)
                ->get(),
        ];

        return view('dashboard.client', compact('stats'));
    }
}
