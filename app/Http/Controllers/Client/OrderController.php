<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Branch;
use App\Models\PromoCode;
use App\Notifications\OrderStatusUpdated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display customer's orders.
     */
    public function index(Request $request)
    {
        $query = Order::where('user_id', auth()->id())
                     ->with(['orderItems.product', 'branch', 'deliveryAssignment.deliveryPerson']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('orderItems.product', function($productQuery) use ($search) {
                      $productQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->latest()->paginate(10);

        return view('client.orders.index', compact('orders'));
    }

    /**
     * Show order details.
     */
    public function show(Order $order)
    {
        // Ensure user can only view their own orders
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        $order->load([
            'orderItems.product', 
            'branch', 
            'deliveryAssignment.deliveryPerson',
            'invoice'
        ]);

        return view('client.orders.show', compact('order'));
    }

    /**
     * Show checkout form.
     */
    public function create(Request $request)
    {
        // Get cart items from session or request
        $cartItems = session('cart', []);
        
        if (empty($cartItems)) {
            return redirect()->route('products.index')
                           ->with('error', 'Your cart is empty. Please add some products first.');
        }

        // Load products with current pricing
        $products = Product::whereIn('id', array_keys($cartItems))
                          ->active()
                          ->get()
                          ->keyBy('id');

        // Calculate totals
        $subtotal = 0;
        $orderItems = [];

        foreach ($cartItems as $productId => $quantity) {
            if (!isset($products[$productId])) {
                continue; // Skip if product not found or inactive
            }

            $product = $products[$productId];
            $price = auth()->user()->isReseller() ? $product->reseller_price : $product->client_price;
            $total = $price * $quantity;

            $orderItems[] = [
                'product' => $product,
                'quantity' => $quantity,
                'unit_price' => $price,
                'total_price' => $total
            ];

            $subtotal += $total;
        }

        $branches = Branch::active()->get();

        return view('client.orders.create', compact('orderItems', 'subtotal', 'branches'));
    }

    /**
     * Store a new order.
     */
    public function store(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'delivery_address' => 'required|string|max:500',
            'delivery_city' => 'required|string|max:100',
            'delivery_latitude' => 'nullable|numeric|between:-90,90',
            'delivery_longitude' => 'nullable|numeric|between:-180,180',
            'payment_method' => 'required|in:cash,check,bank_transfer',
            'promo_code' => 'nullable|string|exists:promo_codes,code',
            'notes' => 'nullable|string|max:1000',
            'cart_items' => 'required|array|min:1',
            'cart_items.*.product_id' => 'required|exists:products,id',
            'cart_items.*.quantity' => 'required|integer|min:1|max:1000'
        ]);

        DB::transaction(function() use ($request) {
            // Validate and calculate order totals
            $subtotal = 0;
            $orderItems = [];
            $user = auth()->user();

            foreach ($request->cart_items as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                if (!$product->is_active) {
                    throw new \Exception("Product {$product->title} is no longer available.");
                }

                $unitPrice = $user->isReseller() ? $product->reseller_price : $product->client_price;
                $totalPrice = $unitPrice * $item['quantity'];

                $orderItems[] = [
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice
                ];

                $subtotal += $totalPrice;
            }

            // Apply promo code if provided
            $discountAmount = 0;
            if ($request->filled('promo_code')) {
                $promoCode = PromoCode::where('code', $request->promo_code)
                                    ->where('is_active', true)
                                    ->where('starts_at', '<=', now())
                                    ->where('expires_at', '>=', now())
                                    ->first();

                if ($promoCode && $promoCode->canBeUsed($subtotal)) {
                    $discountAmount = $promoCode->calculateDiscount($subtotal);
                    $promoCode->increment('used_count');
                }
            }

            $totalAmount = $subtotal - $discountAmount;

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => $user->id,
                'branch_id' => $request->branch_id,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'payment_status' => 'pending',
                'delivery_address' => $request->delivery_address,
                'delivery_city' => $request->delivery_city,
                'delivery_latitude' => $request->delivery_latitude,
                'delivery_longitude' => $request->delivery_longitude,
                'notes' => $request->notes,
                'promo_code' => $request->promo_code,
            ]);

            // Create order items
            foreach ($orderItems as $item) {
                OrderItem::create(array_merge($item, ['order_id' => $order->id]));
            }

            // Clear cart
            session()->forget('cart');

            // Send order confirmation notification to customer
            $user->notify(new OrderStatusUpdated($order, '', 'pending'));

            // Notify administrators about new order
            $admins = \App\Models\User::role(['admin', 'manager'])->get();
            foreach ($admins as $admin) {
                $admin->notify(new OrderStatusUpdated($order, '', 'pending'));
            }

            session()->flash('success', 'Order placed successfully! Order number: ' . $order->order_number);
            session()->flash('order_id', $order->id);
        });

        return redirect()->route('client.orders.index');
    }

    /**
     * Cancel an order.
     */
    public function cancel(Request $request, Order $order)
    {
        // Ensure user can only cancel their own orders
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        if (!$order->canBeCancelled()) {
            return back()->with('error', 'This order cannot be cancelled.');
        }

        $request->validate([
            'cancellation_reason' => 'required|string|max:500'
        ]);

        $order->update([
            'status' => 'cancelled',
            'notes' => ($order->notes ? $order->notes . "\n" : '') . 
                      "Cancelled by customer on " . now()->format('Y-m-d H:i') . ": " . $request->cancellation_reason
        ]);

        return back()->with('success', 'Order cancelled successfully.');
    }

    /**
     * Apply promo code (AJAX).
     */
    public function applyPromoCode(Request $request)
    {
        $request->validate([
            'promo_code' => 'required|string',
            'subtotal' => 'required|numeric|min:0'
        ]);

        $promoCode = PromoCode::where('code', $request->promo_code)
                            ->where('is_active', true)
                            ->where('starts_at', '<=', now())
                            ->where('expires_at', '>=', now())
                            ->first();

        if (!$promoCode) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired promo code.'
            ]);
        }

        if (!$promoCode->canBeUsed($request->subtotal)) {
            return response()->json([
                'success' => false,
                'message' => $promoCode->getUsageErrorMessage($request->subtotal)
            ]);
        }

        $discountAmount = $promoCode->calculateDiscount($request->subtotal);

        return response()->json([
            'success' => true,
            'discount_amount' => $discountAmount,
            'total_amount' => $request->subtotal - $discountAmount,
            'message' => 'Promo code applied successfully!'
        ]);
    }
}
