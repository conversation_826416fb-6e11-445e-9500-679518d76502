<?php

namespace App\Notifications;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrderStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;
    public $oldStatus;
    public $newStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order, string $oldStatus, string $newStatus)
    {
        $this->order = $order;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $statusMessages = [
            'pending' => 'Your order has been received and is being processed.',
            'confirmed' => 'Your order has been confirmed and is being prepared.',
            'picked' => 'Your order has been picked up and is ready for delivery.',
            'out_for_delivery' => 'Your order is out for delivery and will arrive soon.',
            'delivered' => 'Your order has been successfully delivered.',
            'cancelled' => 'Your order has been cancelled.'
        ];

        $message = (new MailMessage)
            ->subject('Order Update - ' . $this->order->order_number)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We have an update on your order #' . $this->order->order_number)
            ->line($statusMessages[$this->newStatus] ?? 'Your order status has been updated.');

        if ($this->newStatus === 'delivered') {
            $message->line('Thank you for choosing YalaOffice! We hope you enjoy your purchase.');
        } elseif ($this->newStatus === 'cancelled') {
            $message->line('If you have any questions about this cancellation, please contact our support team.');
        } else {
            $message->action('View Order Details', route('client.orders.show', $this->order));
        }

        return $message->line('Thank you for using YalaOffice!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'message' => 'Order #' . $this->order->order_number . ' status updated to ' . ucwords(str_replace('_', ' ', $this->newStatus)),
            'action_url' => route('client.orders.show', $this->order)
        ];
    }
}
