<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\BranchController;
use App\Http\Controllers\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\Client\OrderController as ClientOrderController;
use App\Http\Controllers\Delivery\OrderController as DeliveryOrderController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'role:admin|manager'])->prefix('admin')->name('admin.')->group(function () {
    // Products
    Route::resource('products', ProductController::class);
    Route::patch('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');
    Route::patch('products/{product}/toggle-featured', [ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');

    // Categories
    Route::resource('categories', CategoryController::class);
    Route::patch('categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::post('categories/update-sort-order', [CategoryController::class, 'updateSortOrder'])->name('categories.update-sort-order');

    // Branches
    Route::resource('branches', BranchController::class);

    // Orders
    Route::get('orders', [AdminOrderController::class, 'index'])->name('orders.index');
    Route::get('orders/{order}', [AdminOrderController::class, 'show'])->name('orders.show');
    Route::patch('orders/{order}/status', [AdminOrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::patch('orders/{order}/payment-status', [AdminOrderController::class, 'updatePaymentStatus'])->name('orders.update-payment-status');
    Route::post('orders/{order}/assign-delivery', [AdminOrderController::class, 'assignDelivery'])->name('orders.assign-delivery');
    Route::patch('orders/{order}/cancel', [AdminOrderController::class, 'cancel'])->name('orders.cancel');
    Route::get('orders-stats', [AdminOrderController::class, 'getStats'])->name('orders.stats');
});

// Client routes
Route::middleware(['auth', 'role:client|reseller'])->prefix('client')->name('client.')->group(function () {
    // Orders
    Route::get('orders', [ClientOrderController::class, 'index'])->name('orders.index');
    Route::get('orders/create', [ClientOrderController::class, 'create'])->name('orders.create');
    Route::post('orders', [ClientOrderController::class, 'store'])->name('orders.store');
    Route::get('orders/{order}', [ClientOrderController::class, 'show'])->name('orders.show');
    Route::patch('orders/{order}/cancel', [ClientOrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('orders/apply-promo-code', [ClientOrderController::class, 'applyPromoCode'])->name('orders.apply-promo-code');
});

// Delivery routes
Route::middleware(['auth', 'role:delivery'])->prefix('delivery')->name('delivery.')->group(function () {
    // Orders
    Route::get('orders', [DeliveryOrderController::class, 'index'])->name('orders.index');
    Route::get('orders/{order}', [DeliveryOrderController::class, 'show'])->name('orders.show');
    Route::patch('orders/{order}/status', [DeliveryOrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::post('orders/{order}/report-problem', [DeliveryOrderController::class, 'reportProblem'])->name('orders.report-problem');
    Route::get('orders-route', [DeliveryOrderController::class, 'getRoute'])->name('orders.route');
    Route::get('orders-stats', [DeliveryOrderController::class, 'getStats'])->name('orders.stats');
});

require __DIR__.'/auth.php';
