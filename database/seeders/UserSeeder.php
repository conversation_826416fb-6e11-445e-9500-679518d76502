<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+212600000000',
                'address' => 'Casablanca, Morocco',
                'city' => 'Casablanca',
                'user_type' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
        $admin->assignRole('admin');

        // Create manager user
        $manager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Store Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+212600000001',
                'address' => 'Rabat, Morocco',
                'city' => 'Rabat',
                'user_type' => 'manager',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
        $manager->assignRole('manager');

        // Create delivery person
        $delivery = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Delivery Person',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+212600000002',
                'address' => 'Casablanca, Morocco',
                'city' => 'Casablanca',
                'user_type' => 'delivery',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
        $delivery->assignRole('delivery');

        // Create sample client
        $client = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sample Client',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+212600000003',
                'address' => 'Marrakech, Morocco',
                'city' => 'Marrakech',
                'user_type' => 'client',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
        $client->assignRole('client');

        // Create sample reseller
        $reseller = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sample Reseller',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+212600000004',
                'address' => 'Fez, Morocco',
                'city' => 'Fez',
                'user_type' => 'reseller',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
        $reseller->assignRole('reseller');
    }
}
