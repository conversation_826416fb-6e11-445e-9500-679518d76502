<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $roles = [
            'admin' => 'System Administrator',
            'manager' => 'Store Manager',
            'delivery' => 'Delivery Person',
            'client' => 'Normal Client',
            'reseller' => 'Reseller Client'
        ];

        foreach ($roles as $name => $description) {
            Role::firstOrCreate(['name' => $name], ['name' => $name]);
        }

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Product management
            'view products',
            'create products',
            'edit products',
            'delete products',

            // Category management
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',

            // Branch management
            'view branches',
            'create branches',
            'edit branches',
            'delete branches',

            // Order management
            'view orders',
            'create orders',
            'edit orders',
            'delete orders',
            'assign delivery',

            // Invoice management
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',

            // Analytics
            'view analytics',
            'view reports',

            // Promo codes
            'view promo codes',
            'create promo codes',
            'edit promo codes',
            'delete promo codes',

            // Reviews
            'view reviews',
            'approve reviews',
            'delete reviews',

            // System settings
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission], ['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo(Permission::all());

        $managerRole = Role::findByName('manager');
        $managerRole->givePermissionTo([
            'view users', 'create users', 'edit users',
            'view products', 'create products', 'edit products', 'delete products',
            'view categories', 'create categories', 'edit categories', 'delete categories',
            'view branches', 'create branches', 'edit branches', 'delete branches',
            'view orders', 'create orders', 'edit orders', 'assign delivery',
            'view invoices', 'create invoices', 'edit invoices',
            'view analytics', 'view reports',
            'view promo codes', 'create promo codes', 'edit promo codes', 'delete promo codes',
            'view reviews', 'approve reviews', 'delete reviews',
        ]);

        $deliveryRole = Role::findByName('delivery');
        $deliveryRole->givePermissionTo([
            'view orders',
            'edit orders', // For updating delivery status
        ]);

        // Client and reseller roles have basic permissions (handled in controllers)
    }
}
