<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Branch;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $branches = [
            [
                'name' => 'YalaOffice Casablanca',
                'code' => 'CASA01',
                'address' => 'Boulevard Mohammed V, Casablanca',
                'city' => 'Casablanca',
                'phone' => '+212522000001',
                'email' => '<EMAIL>',
                'latitude' => 33.5731,
                'longitude' => -7.5898,
                'is_active' => true,
            ],
            [
                'name' => 'YalaOffice Rabat',
                'code' => 'RABAT01',
                'address' => 'Avenue Mohammed V, Rabat',
                'city' => 'Rabat',
                'phone' => '+212537000001',
                'email' => '<EMAIL>',
                'latitude' => 34.0209,
                'longitude' => -6.8416,
                'is_active' => true,
            ],
            [
                'name' => 'YalaOffice Marrakech',
                'code' => 'MARR01',
                'address' => 'Avenue Mohammed VI, Marrakech',
                'city' => 'Marrakech',
                'phone' => '+212524000001',
                'email' => '<EMAIL>',
                'latitude' => 31.6295,
                'longitude' => -7.9811,
                'is_active' => true,
            ],
            [
                'name' => 'YalaOffice Fez',
                'code' => 'FEZ01',
                'address' => 'Boulevard Allal Ben Abdellah, Fez',
                'city' => 'Fez',
                'phone' => '+212535000001',
                'email' => '<EMAIL>',
                'latitude' => 34.0181,
                'longitude' => -5.0078,
                'is_active' => true,
            ],
        ];

        foreach ($branches as $branchData) {
            Branch::firstOrCreate(
                ['code' => $branchData['code']],
                $branchData
            );
        }
    }
}
