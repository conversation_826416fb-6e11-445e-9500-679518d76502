<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Office Supplies',
                'description' => 'Essential office supplies for daily operations',
                'children' => [
                    'Stationery',
                    'Paper Products',
                    'Writing Instruments',
                    'Desk Accessories',
                    'Filing & Storage'
                ]
            ],
            [
                'name' => 'School Supplies',
                'description' => 'Educational materials and school essentials',
                'children' => [
                    'Notebooks & Journals',
                    'Art Supplies',
                    'Backpacks & Bags',
                    'Educational Tools',
                    'Sports Equipment'
                ]
            ],
            [
                'name' => 'Technology',
                'description' => 'Electronic devices and accessories',
                'children' => [
                    'Computers & Laptops',
                    'Printers & Scanners',
                    'Cables & Accessories',
                    'Software',
                    'Mobile Accessories'
                ]
            ],
            [
                'name' => 'Furniture',
                'description' => 'Office and classroom furniture',
                'children' => [
                    'Desks & Tables',
                    'Chairs & Seating',
                    'Storage Solutions',
                    'Whiteboards & Boards',
                    'Lighting'
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $parent = Category::firstOrCreate([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => $categoryData['description'],
                'is_active' => true,
                'sort_order' => 0,
            ]);

            foreach ($categoryData['children'] as $index => $childName) {
                Category::firstOrCreate([
                    'name' => $childName,
                    'slug' => Str::slug($childName),
                    'parent_id' => $parent->id,
                    'is_active' => true,
                    'sort_order' => $index + 1,
                ]);
            }
        }
    }
}
