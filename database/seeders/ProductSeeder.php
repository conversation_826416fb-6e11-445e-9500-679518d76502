<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;
use App\Models\Branch;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $branches = Branch::all();

        if ($categories->isEmpty() || $branches->isEmpty()) {
            $this->command->warn('Please run CategorySeeder and BranchSeeder first.');
            return;
        }

        $products = [
            [
                'title' => 'A4 Copy Paper - 500 Sheets',
                'description' => 'High-quality white A4 copy paper, perfect for printing and copying. 80gsm weight.',
                'sku' => 'PAPER-A4-500',
                'brand' => 'PaperPro',
                'normal_price' => 45.00,
                'reseller_price' => 38.00,
                'current_stock' => 150,
                'minimum_stock' => 20,
                'tags' => ['paper', 'office', 'printing'],
                'is_featured' => true,
            ],
            [
                'title' => 'Blue Ballpoint Pens - Pack of 10',
                'description' => 'Smooth writing blue ballpoint pens. Comfortable grip and long-lasting ink.',
                'sku' => 'PEN-BLUE-10',
                'brand' => 'WriteMaster',
                'normal_price' => 25.00,
                'reseller_price' => 20.00,
                'current_stock' => 80,
                'minimum_stock' => 15,
                'tags' => ['pens', 'writing', 'office'],
                'is_featured' => true,
            ],
            [
                'title' => 'Spiral Notebook - 200 Pages',
                'description' => 'A5 spiral-bound notebook with lined pages. Perfect for note-taking and journaling.',
                'sku' => 'NOTEBOOK-A5-200',
                'brand' => 'StudyMate',
                'normal_price' => 18.00,
                'reseller_price' => 15.00,
                'current_stock' => 120,
                'minimum_stock' => 25,
                'tags' => ['notebook', 'school', 'writing'],
                'is_featured' => false,
            ],
            [
                'title' => 'Stapler with Staples',
                'description' => 'Heavy-duty metal stapler with 1000 staples included. Suitable for up to 25 sheets.',
                'sku' => 'STAPLER-HD-001',
                'brand' => 'OfficeMax',
                'normal_price' => 65.00,
                'reseller_price' => 55.00,
                'current_stock' => 45,
                'minimum_stock' => 10,
                'tags' => ['stapler', 'office', 'binding'],
                'is_featured' => false,
            ],
            [
                'title' => 'Highlighter Set - 6 Colors',
                'description' => 'Set of 6 fluorescent highlighters in different colors. Perfect for studying and organizing.',
                'sku' => 'HIGHLIGHT-6COL',
                'brand' => 'ColorBright',
                'normal_price' => 32.00,
                'reseller_price' => 27.00,
                'current_stock' => 90,
                'minimum_stock' => 20,
                'tags' => ['highlighter', 'school', 'colors'],
                'is_featured' => true,
            ],
            [
                'title' => 'File Folders - Pack of 25',
                'description' => 'Manila file folders for document organization. Letter size with tab labels.',
                'sku' => 'FOLDER-MAN-25',
                'brand' => 'OrganizePro',
                'normal_price' => 55.00,
                'reseller_price' => 47.00,
                'current_stock' => 60,
                'minimum_stock' => 15,
                'tags' => ['folders', 'filing', 'organization'],
                'is_featured' => false,
            ],
            [
                'title' => 'Calculator - Scientific',
                'description' => 'Advanced scientific calculator with 240+ functions. Perfect for students and professionals.',
                'sku' => 'CALC-SCI-240',
                'brand' => 'MathPro',
                'normal_price' => 120.00,
                'reseller_price' => 100.00,
                'current_stock' => 25,
                'minimum_stock' => 5,
                'tags' => ['calculator', 'math', 'scientific'],
                'is_featured' => true,
            ],
            [
                'title' => 'Whiteboard Markers - Set of 4',
                'description' => 'Dry erase markers in black, blue, red, and green. Low-odor and easy to erase.',
                'sku' => 'MARKER-WB-4',
                'brand' => 'BoardMaster',
                'normal_price' => 28.00,
                'reseller_price' => 24.00,
                'current_stock' => 70,
                'minimum_stock' => 18,
                'tags' => ['markers', 'whiteboard', 'presentation'],
                'is_featured' => false,
            ],
            [
                'title' => 'Desk Organizer - 6 Compartments',
                'description' => 'Wooden desk organizer with 6 compartments for pens, papers, and office supplies.',
                'sku' => 'ORGANIZER-WOOD-6',
                'brand' => 'DeskMate',
                'normal_price' => 85.00,
                'reseller_price' => 72.00,
                'current_stock' => 30,
                'minimum_stock' => 8,
                'tags' => ['organizer', 'desk', 'storage'],
                'is_featured' => false,
            ],
            [
                'title' => 'Correction Tape - Pack of 5',
                'description' => 'White correction tape for clean corrections. Easy to use and mess-free.',
                'sku' => 'CORRECT-TAPE-5',
                'brand' => 'CorrectIt',
                'normal_price' => 22.00,
                'reseller_price' => 18.00,
                'current_stock' => 100,
                'minimum_stock' => 25,
                'tags' => ['correction', 'tape', 'office'],
                'is_featured' => false,
            ],
        ];

        foreach ($products as $productData) {
            // Get random category and branch
            $category = $categories->where('parent_id', '!=', null)->random();
            $branch = $branches->random();

            Product::create([
                'title' => $productData['title'],
                'slug' => Str::slug($productData['title']),
                'description' => $productData['description'],
                'sku' => $productData['sku'],
                'category_id' => $category->id,
                'branch_id' => $branch->id,
                'brand' => $productData['brand'],
                'normal_price' => $productData['normal_price'],
                'reseller_price' => $productData['reseller_price'],
                'current_stock' => $productData['current_stock'],
                'minimum_stock' => $productData['minimum_stock'],
                'tags' => $productData['tags'],
                'is_active' => true,
                'is_featured' => $productData['is_featured'],
            ]);
        }

        $this->command->info('Created ' . count($products) . ' sample products.');
    }
}
